<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加载更多功能测试</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .product-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .product-image {
            aspect-ratio: 1/1;
            height: auto;
            background-size: cover;
            background-position: center;
            transition: transform 0.5s ease;
            cursor: pointer;
        }
        
        .product-card:hover .product-image {
            transform: scale(1.05);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .price-tag {
            background: rgba(0, 0, 0, 0.7);
            color: white;
        }
        
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #f44336;
            color: white;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <!-- 引入通用的图片检测功能 -->
    <script src="assets/js/main.js"></script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-black bg-opacity-70 shadow-sm py-4 backdrop-filter backdrop-blur-md">
        <div class="container mx-auto px-4">
            <h1 class="text-2xl font-bold text-white text-center">加载更多功能测试页面</h1>
        </div>
    </header>

    <!-- Main Content -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-white mb-4">测试产品列表</h2>
                <p class="text-gray-200">滚动到底部或点击"加载更多"按钮来测试懒加载功能</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- 初始产品 1-12 -->
                <div class="product-card animate__animated animate__fadeIn rounded-lg overflow-hidden" data-id="1" data-name="Test Product 1" data-price="39.99" data-image="assets/img/cap/Baseball/list2/1.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/Baseball/list2/1"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold">-20%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold text-white">Test Product 1</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$39.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3">测试产品描述</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded">Premium</span>
                            <span class="tag text-xs px-2 py-1 rounded">Quality</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> 添加到购物车
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 更多初始产品... -->
                <div class="product-card animate__animated animate__fadeIn rounded-lg overflow-hidden" data-id="2" data-name="Test Product 2" data-price="45.99" data-image="assets/img/cap/Baseball/list2/2.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/Baseball/list2/2"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold text-white">Test Product 2</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$45.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3">测试产品描述</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded">Sporty</span>
                            <span class="tag text-xs px-2 py-1 rounded">Active</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> 添加到购物车
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多按钮 -->
            <div class="flex justify-center mt-12">
                <button id="loadMore" class="px-6 py-2 bg-black bg-opacity-50 text-white rounded-lg shadow-sm hover:bg-opacity-70 transition flex items-center">
                    <span>加载更多</span>
                    <div id="spinner" class="ml-2 hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            </div>
        </div>
    </section>

    <script>
        // 测试脚本
        console.log('测试页面加载完成');
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化');
            
            // 检测并设置所有产品图片
            if (typeof window.initializeProductImages === 'function') {
                window.initializeProductImages();
                console.log('产品图片初始化完成');
            } else {
                console.error('图片检测功能未加载');
            }
            
            // 初始化懒加载功能
            if (typeof window.initializeLazyLoading === 'function') {
                console.log('初始化懒加载功能');
                const cleanup = window.initializeLazyLoading('assets/img/cap/Baseball/list2', {
                    threshold: 200, // 距离底部200像素时触发
                    productGridSelector: '.grid',
                    loadMoreButtonSelector: '#loadMore',
                    spinnerSelector: '#spinner'
                });
                
                // 页面卸载时清理
                window.addEventListener('beforeunload', function() {
                    if (cleanup && typeof cleanup === 'function') {
                        cleanup();
                        console.log('懒加载监听器已清理');
                    }
                });
            } else {
                console.error('懒加载功能未加载');
            }
            
            // 加载更多按钮点击事件
            document.getElementById('loadMore').addEventListener('click', async function() {
                try {
                    console.log('用户点击加载更多按钮');
                    const result = await window.loadMoreProducts('assets/img/cap/Baseball/list2');
                    console.log('加载更多产品结果:', result);
                    
                    if (result.loaded > 0) {
                        console.log(`成功加载 ${result.loaded} 个新产品!`);
                    }
                } catch (error) {
                    console.error('加载更多产品失败:', error);
                }
            });
        });
    </script>
</body>
</html>
