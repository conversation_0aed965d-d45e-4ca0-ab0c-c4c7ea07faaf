/**
* Template Name: Dewi
* Template URL: https://bootstrapmade.com/dewi-free-multi-purpose-html-template/
* Updated: Aug 07 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

// 图片格式检测和设置函数 - 通用版本
window.detectAndSetImage = function(element, basePath) {
    const extensions = ['jpg', 'jpeg', 'png', 'webp', 'avif']; // 支持的图片格式

    // 创建一个测试图片来检测文件是否存在
    function testImage(url) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }

    // 按优先级顺序测试每种格式
    async function findImage() {
        for (const ext of extensions) {
            const imageUrl = `${basePath}.${ext}`;
            const exists = await testImage(imageUrl);
            if (exists) {
                element.style.backgroundImage = `url('${imageUrl}')`;
                console.log(`找到图片: ${imageUrl}`);
                return true;
            }
        }

        // 如果没有找到任何格式的图片，使用默认占位符
        console.warn(`未找到图片: ${basePath}.*`);
        element.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'300\' height=\'300\' viewBox=\'0 0 300 300\'%3E%3Crect width=\'300\' height=\'300\' fill=\'%23f0f0f0\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'16\' fill=\'%23999\'%3E图片未找到%3C/text%3E%3C/svg%3E")';
        return false;
    }

    return findImage();
};

// 批量检测和设置所有产品图片的函数
window.initializeProductImages = function() {
    console.log('开始检测产品图片格式');
    document.querySelectorAll('.product-image[data-image-base]').forEach(async (element) => {
        const basePath = element.getAttribute('data-image-base');
        if (basePath) {
            await window.detectAndSetImage(element, basePath);
        }
    });
};

// 专门用于模态框的图片检测函数
window.detectModalImage = function(basePath) {
    const extensions = ['jpg', 'jpeg', 'png', 'webp', 'avif']; // 支持的图片格式

    // 创建一个测试图片来检测文件是否存在
    function testImage(url) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }

    // 按优先级顺序测试每种格式
    async function findImage() {
        for (const ext of extensions) {
            const imageUrl = `${basePath}.${ext}`;
            const exists = await testImage(imageUrl);
            if (exists) {
                console.log(`模态框找到图片: ${imageUrl}`);
                return imageUrl;
            }
        }

        // 如果没有找到任何格式的图片，返回默认占位符
        console.warn(`模态框未找到图片: ${basePath}.*`);
        return 'data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'400\' height=\'400\' viewBox=\'0 0 400 400\'%3E%3Crect width=\'400\' height=\'400\' fill=\'%23f0f0f0\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'20\' fill=\'%23999\'%3E图片未找到%3C/text%3E%3C/svg%3E';
    }

    return findImage();
};

// 检测产品的子图片（如13-1.jpg, 13-2.jpg等）
window.detectSubImages = function(basePath) {
    const extensions = ['jpg', 'jpeg', 'png', 'webp', 'avif'];

    // 创建一个测试图片来检测文件是否存在
    function testImage(url) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }

    async function findSubImages() {
        const subImages = [];

        // 检测从-1到-10的子图片
        for (let i = 1; i <= 10; i++) {
            for (const ext of extensions) {
                const subImageUrl = `${basePath}-${i}.${ext}`;
                const exists = await testImage(subImageUrl);
                if (exists) {
                    console.log(`找到子图片: ${subImageUrl}`);
                    subImages.push({
                        url: subImageUrl,
                        index: i,
                        extension: ext
                    });
                    break; // 找到一个格式就跳出内层循环
                }
            }
        }

        console.log(`总共找到 ${subImages.length} 个子图片`);
        return subImages;
    }

    return findSubImages();
};

// 创建和管理子图片缩略图的函数
window.createSubImageThumbnails = function(subImages, mainImageElement, modalContainer) {
    // 移除现有的缩略图容器
    const existingThumbnails = modalContainer.querySelector('.sub-images-container');
    if (existingThumbnails) {
        existingThumbnails.remove();
    }

    if (subImages.length === 0) {
        return; // 没有子图片，不创建缩略图
    }

    console.log('创建缩略图，子图片列表:', subImages.map(img => img.url));

    // 创建缩略图容器
    const thumbnailContainer = document.createElement('div');
    thumbnailContainer.className = 'sub-images-container';
    thumbnailContainer.style.cssText = `
        margin-top: 15px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
        padding: 10px;
        background: rgba(0,0,0,0.1);
        border-radius: 8px;
    `;

    // 添加标题
    const title = document.createElement('div');
    title.textContent = '更多图片:';
    title.style.cssText = `
        width: 100%;
        text-align: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        font-weight: bold;
    `;
    thumbnailContainer.appendChild(title);

    // 创建缩略图网格容器
    const thumbnailGrid = document.createElement('div');
    thumbnailGrid.style.cssText = `
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
        width: 100%;
    `;

    // 为每个子图片创建缩略图
    subImages.forEach((subImage, index) => {
        const thumbnail = document.createElement('img');
        // 确保使用正确的子图片URL
        thumbnail.src = subImage.url;
        thumbnail.alt = `子图片 ${subImage.index}`;
        thumbnail.dataset.originalUrl = subImage.url; // 保存原始URL
        thumbnail.style.cssText = `
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            opacity: 0.8;
        `;

        console.log(`创建缩略图 ${index + 1}: URL=${subImage.url}, 实际src=${thumbnail.src}`);

        // 悬浮效果
        thumbnail.addEventListener('mouseenter', function() {
            this.style.opacity = '1';
            this.style.transform = 'scale(1.1)';
            this.style.border = '2px solid #007bff';
        });

        thumbnail.addEventListener('mouseleave', function() {
            this.style.opacity = '0.8';
            this.style.transform = 'scale(1)';
            this.style.border = '2px solid transparent';
        });

        // 点击事件：与主图互换
        thumbnail.addEventListener('click', function() {
            const currentMainSrc = mainImageElement.src;
            const newMainSrc = this.src;

            console.log(`点击缩略图，准备互换: 主图 ${currentMainSrc} <-> 缩略图 ${newMainSrc}`);

            // 创建淡出淡入效果
            mainImageElement.style.transition = 'opacity 0.3s ease';
            mainImageElement.style.opacity = '0';

            setTimeout(() => {
                // 互换图片
                mainImageElement.src = newMainSrc;
                this.src = currentMainSrc;

                // 淡入新图片
                mainImageElement.style.opacity = '1';

                console.log(`图片已互换: 主图现在显示 ${newMainSrc}，缩略图现在显示 ${currentMainSrc}`);
            }, 300);
        });

        thumbnailGrid.appendChild(thumbnail);
    });

    thumbnailContainer.appendChild(thumbnailGrid);

    // 将缩略图容器添加到模态框中（在主图片下方）
    const imageContainer = mainImageElement.parentElement;
    imageContainer.parentElement.insertBefore(thumbnailContainer, imageContainer.nextSibling);

    console.log(`已创建 ${subImages.length} 个缩略图`);
};

// 检测视频文件是否存在
window.detectVideo = function(basePath) {
    return new Promise((resolve) => {
        const video = document.createElement('video');
        const videoUrl = `${basePath}.mp4`;

        video.onloadedmetadata = () => {
            console.log(`找到视频文件: ${videoUrl}`);
            resolve(videoUrl);
        };

        video.onerror = () => {
            console.log(`未找到视频文件: ${videoUrl}`);
            resolve(null);
        };

        video.src = videoUrl;
    });
};

// 检测目录中的数字图片文件
window.detectNumberedImages = function(basePath, startNumber = 13) {
    const extensions = ['jpg', 'jpeg', 'png', 'webp', 'avif']; // 支持的图片格式

    // 创建一个测试图片来检测文件是否存在
    function testImage(url) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }

    // 检测指定编号的图片是否存在
    async function checkImageExists(number) {
        for (const ext of extensions) {
            const imageUrl = `${basePath}/${number}.${ext}`;
            const exists = await testImage(imageUrl);
            if (exists) {
                console.log(`找到编号图片: ${imageUrl}`);
                return imageUrl;
            }
        }
        return null;
    }

    // 批量检测从startNumber开始的连续图片
    async function findConsecutiveImages() {
        const foundImages = [];
        let currentNumber = startNumber;
        let consecutiveNotFound = 0;
        const maxGap = 5; // 最多允许5个连续的空缺

        while (consecutiveNotFound < maxGap && currentNumber <= startNumber + 50) { // 最多检测50张图片
            const imageUrl = await checkImageExists(currentNumber);
            if (imageUrl) {
                foundImages.push({
                    number: currentNumber,
                    url: imageUrl,
                    basePath: `${basePath}/${currentNumber}`
                });
                consecutiveNotFound = 0; // 重置连续未找到计数
                console.log(`检测到图片 ${currentNumber}: ${imageUrl}`);
            } else {
                consecutiveNotFound++;
                console.log(`未找到图片 ${currentNumber}，连续未找到: ${consecutiveNotFound}`);
            }
            currentNumber++;
        }

        console.log(`图片检测完成，共找到 ${foundImages.length} 张图片`);
        return foundImages;
    }

    return findConsecutiveImages();
};

// 模态框媒体检测和播放函数（支持视频+图片+子图片）
window.detectAndPlayModalMedia = function(basePath, modalImageElement) {
    return new Promise(async (resolve) => {
        try {
            // 首先检测是否有视频文件
            const videoUrl = await window.detectVideo(basePath);

            if (videoUrl) {
                // 如果有视频，先播放视频
                console.log('开始播放产品视频');
                await playVideoThenImage(videoUrl, basePath, modalImageElement);
            } else {
                // 如果没有视频，直接显示图片
                console.log('没有视频文件，直接显示图片');
                const imageUrl = await window.detectModalImage(basePath);
                modalImageElement.src = imageUrl;

                // 设置图片样式
                modalImageElement.style.width = '100%';
                modalImageElement.style.height = '100%';
                modalImageElement.style.objectFit = 'cover';
                modalImageElement.style.position = 'relative';
                modalImageElement.style.zIndex = '1';
                modalImageElement.style.opacity = '1';

                // 确保容器样式正确
                const container = modalImageElement.parentElement;
                container.style.position = 'relative';
                container.style.overflow = 'hidden';
            }

            // 检测子图片并创建缩略图
            console.log('开始检测子图片，basePath:', basePath);
            const subImages = await window.detectSubImages(basePath);

            if (subImages.length > 0) {
                console.log(`找到 ${subImages.length} 个子图片，详细信息:`, subImages);
                // 获取模态框容器
                const modalContainer = modalImageElement.closest('.modal-content') ||
                                     modalImageElement.closest('.product-modal') ||
                                     modalImageElement.closest('[class*="modal"]');

                if (modalContainer) {
                    window.createSubImageThumbnails(subImages, modalImageElement, modalContainer);
                } else {
                    console.warn('未找到模态框容器，无法创建缩略图');
                }
            } else {
                console.log('没有找到子图片，basePath:', basePath);
            }

            resolve();
        } catch (error) {
            console.error('模态框媒体检测出错:', error);
            // 出错时回退到图片
            const imageUrl = await window.detectModalImage(basePath);
            modalImageElement.src = imageUrl;

            // 设置图片样式
            modalImageElement.style.width = '100%';
            modalImageElement.style.height = '100%';
            modalImageElement.style.objectFit = 'cover';
            modalImageElement.style.position = 'relative';
            modalImageElement.style.zIndex = '1';
            modalImageElement.style.opacity = '1';

            // 确保容器样式正确
            const container = modalImageElement.parentElement;
            container.style.position = 'relative';
            container.style.overflow = 'hidden';

            resolve();
        }
    });
};

// 播放视频然后淡化到图片的函数
async function playVideoThenImage(videoUrl, basePath, modalImageElement) {
    return new Promise(async (resolve) => {
        // 创建视频元素
        const video = document.createElement('video');
        video.src = videoUrl;
        video.autoplay = true;
        video.muted = true; // 静音自动播放

        // 设置视频样式，确保与图片尺寸完全一致
        video.style.width = '100%';
        video.style.height = '100%';
        video.style.objectFit = 'cover'; // 保持宽高比，填满容器
        video.style.position = 'absolute';
        video.style.top = '0';
        video.style.left = '0';
        video.style.zIndex = '10';
        video.style.opacity = '1';
        video.style.transition = 'opacity 1s ease-in-out';
        video.style.borderRadius = 'inherit'; // 继承容器的圆角

        // 获取图片URL
        const imageUrl = await window.detectModalImage(basePath);

        // 预加载图片并设置样式
        modalImageElement.src = imageUrl;
        modalImageElement.style.opacity = '0';
        modalImageElement.style.width = '100%';
        modalImageElement.style.height = '100%';
        modalImageElement.style.objectFit = 'cover'; // 确保图片也使用相同的填充方式
        modalImageElement.style.position = 'relative';
        modalImageElement.style.zIndex = '1';

        // 将视频添加到模态框图片容器中
        const container = modalImageElement.parentElement;
        container.style.position = 'relative';
        container.style.overflow = 'hidden'; // 确保视频不会溢出容器
        container.appendChild(video);

        // 视频播放完成后的处理
        video.onended = () => {
            console.log('视频播放完成，开始淡化到图片');
            fadeVideoToImage(video, modalImageElement, resolve);
        };

        // 视频加载出错的处理
        video.onerror = () => {
            console.error('视频播放出错，直接显示图片');
            if (container.contains(video)) {
                container.removeChild(video);
            }

            // 确保图片有正确的样式
            modalImageElement.style.width = '100%';
            modalImageElement.style.height = '100%';
            modalImageElement.style.objectFit = 'cover';
            modalImageElement.style.position = 'relative';
            modalImageElement.style.zIndex = '1';
            modalImageElement.style.opacity = '1';

            resolve();
        };

        // 设置最大播放时间（防止视频过长）
        setTimeout(() => {
            if (!video.ended && !video.paused) {
                console.log('视频播放时间过长，强制淡化到图片');
                fadeVideoToImage(video, modalImageElement, resolve);
            }
        }, 5000); // 最多播放5秒
    });
}

// 视频淡化到图片的动画函数
function fadeVideoToImage(video, modalImageElement, callback) {
    // 开始淡化视频
    video.style.opacity = '0';

    // 同时淡入图片
    setTimeout(() => {
        modalImageElement.style.opacity = '1';
    }, 200);

    // 动画完成后移除视频元素
    setTimeout(() => {
        const container = video.parentElement;
        if (container && container.contains(video)) {
            container.removeChild(video);
        }
        callback();
    }, 1000);
}

// 生成产品卡片HTML的函数
window.generateProductCard = function(productData, index) {
    const {number, url, basePath} = productData;
    const productId = `loaded_${number}`;
    const productName = `Baseball Cap Style 2 - ${number}`;
    const basePrice = 35.99;
    const priceVariation = (number % 5) * 5; // 根据编号创建价格变化
    const price = (basePrice + priceVariation).toFixed(2);

    // 随机生成折扣标签
    const hasDiscount = number % 3 === 0; // 每3个产品有一个折扣
    const discountPercent = hasDiscount ? (10 + (number % 3) * 5) : 0;

    // 生成标签
    const tags = [
        ['Premium', 'Quality', 'Stylish'],
        ['Sporty', 'Active', 'Durable'],
        ['Classic', 'Timeless', 'Elegant'],
        ['Modern', 'Trendy', 'Fashion'],
        ['Vintage', 'Retro', 'Unique']
    ];
    const tagSet = tags[number % tags.length];

    const cardHTML = `
        <div class="product-card animate__animated animate__fadeIn"
             data-id="${productId}"
             data-name="${productName}"
             data-price="${price}"
             data-image="${url}"
             style="animation-delay: ${index * 0.1}s;">
            <div class="relative overflow-hidden">
                <div class="product-image" data-image-base="${basePath}"></div>
                ${hasDiscount ? `<div class="discount-badge px-2 py-1 rounded text-xs font-bold">-${discountPercent}%</div>` : ''}
            </div>
            <div class="p-4 flex-grow flex flex-col">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold">${productName}</h3>
                    <div class="price-tag px-2 py-1 rounded text-sm font-bold">$${price}</div>
                </div>
                <p class="text-gray-200 text-sm mb-3">Premium quality baseball cap with modern design.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    ${tagSet.map(tag => `<span class="tag text-xs px-2 py-1 rounded">${tag}</span>`).join('')}
                </div>
                <div class="flex justify-between mt-auto pt-4">
                    <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                        <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                    </button>
                    <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    return cardHTML;
};

// 全局状态管理
window.loadMoreState = {
    loadedImageNumbers: new Set(), // 已加载的图片编号
    currentStartNumber: 13, // 当前开始检测的编号
    isLoading: false, // 是否正在加载
    hasMoreContent: true, // 是否还有更多内容
    lastLoadTime: 0, // 上次加载时间
    minLoadInterval: 2000, // 最小加载间隔（毫秒）
    pageStates: {} // 每个页面的独立状态
};

// 重置页面加载状态
window.resetLoadMoreState = function(imageFolderPath) {
    console.log('重置页面加载状态:', imageFolderPath);

    if (!window.loadMoreState.pageStates) {
        window.loadMoreState.pageStates = {};
    }

    window.loadMoreState.pageStates[imageFolderPath] = {
        loadedImageNumbers: new Set(),
        currentStartNumber: 13,
        hasMoreContent: true,
        lastLoadTime: 0
    };

    // 同时重置全局状态
    window.loadMoreState.loadedImageNumbers = new Set();
    window.loadMoreState.currentStartNumber = 13;
    window.loadMoreState.hasMoreContent = true;
    window.loadMoreState.isLoading = false;
    window.loadMoreState.lastLoadTime = 0;

    console.log('页面加载状态已重置');
};

// 显示加载进度提示
window.showLoadingProgress = function(message, type = 'info') {
    // 移除现有的进度提示
    const existingProgress = document.querySelector('.loading-progress');
    if (existingProgress) {
        existingProgress.remove();
    }

    const progressDiv = document.createElement('div');
    progressDiv.className = 'loading-progress fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 animate__animated animate__fadeIn';

    // 根据类型设置样式
    switch (type) {
        case 'success':
            progressDiv.classList.add('bg-green-600', 'text-white');
            progressDiv.innerHTML = `<i class="fas fa-check mr-2"></i>${message}`;
            break;
        case 'error':
            progressDiv.classList.add('bg-red-600', 'text-white');
            progressDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
            break;
        case 'loading':
            progressDiv.classList.add('bg-blue-600', 'text-white');
            progressDiv.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${message}`;
            break;
        case 'complete':
            progressDiv.classList.add('bg-gray-600', 'text-white');
            progressDiv.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            break;
        default:
            progressDiv.classList.add('bg-gray-600', 'text-white');
            progressDiv.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
    }

    document.body.appendChild(progressDiv);

    // 自动移除（除了加载状态）
    if (type !== 'loading') {
        setTimeout(() => {
            if (progressDiv.parentNode) {
                progressDiv.classList.add('animate__fadeOut');
                setTimeout(() => {
                    if (progressDiv.parentNode) {
                        progressDiv.parentNode.removeChild(progressDiv);
                    }
                }, 300);
            }
        }, 3000);
    }

    return progressDiv;
};

// 初始化产品网格 - 根据文件夹中的图片数量设置栏位
window.initializeProductGrid = function(imageFolderPath, productGridSelector = '.grid') {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('初始化产品网格，图片路径:', imageFolderPath);

            const productGrid = document.querySelector(productGridSelector);
            if (!productGrid) {
                throw new Error('未找到产品网格容器');
            }

            // 检测文件夹中所有的数字图片（从1开始）
            const allImages = await window.detectNumberedImages(imageFolderPath, 1);

            console.log(`检测到 ${allImages.length} 个数字图片`);

            if (allImages.length === 0) {
                console.log('文件夹中没有找到数字图片');
                resolve({ total: 0 });
                return;
            }

            // 清空现有的产品网格（保留前12个，它们是HTML中预设的）
            const existingProducts = productGrid.querySelectorAll('.product-card');
            const presetCount = Math.min(12, allImages.length);

            // 如果图片数量超过12个，需要添加额外的栏位
            if (allImages.length > 12) {
                const additionalImages = allImages.slice(12); // 从第13个开始

                for (let i = 0; i < additionalImages.length; i++) {
                    const productData = additionalImages[i];
                    const cardHTML = window.generateProductCard(productData, i);

                    // 创建临时容器来解析HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = cardHTML;
                    const productCard = tempDiv.firstElementChild;

                    // 直接添加到产品网格，无任何动画效果
                    productGrid.appendChild(productCard);

                    // 异步加载产品图片
                    const productImage = productCard.querySelector('.product-image');
                    if (productImage && typeof window.detectAndSetImage === 'function') {
                        window.detectAndSetImage(productImage, productData.basePath);
                    }
                }

                console.log(`添加了 ${additionalImages.length} 个额外的产品栏位`);
            }

            // 重新初始化所有产品的交互功能
            if (typeof window.initializeNewProducts === 'function') {
                window.initializeNewProducts();
            }

            // 应用翻译到新元素
            if (typeof window.applyTranslations === 'function') {
                window.applyTranslations();
            }

            resolve({ total: allImages.length, preset: presetCount, added: Math.max(0, allImages.length - 12) });

        } catch (error) {
            console.error('初始化产品网格时出错:', error);
            reject(error);
        }
    });
};

// 简化的初始化函数 - 不再需要懒加载和动画效果
window.initializeSimpleGrid = function(imageFolderPath) {
    console.log('初始化简单产品网格');

    // 隐藏所有加载相关的按钮和文字
    const loadMoreButton = document.querySelector('#loadMore');
    if (loadMoreButton) {
        loadMoreButton.style.display = 'none';
    }

    const spinner = document.querySelector('#spinner');
    if (spinner) {
        spinner.style.display = 'none';
    }

    // 移除任何现有的提示信息
    const existingMessages = document.querySelectorAll('.loading-progress, .no-more-message');
    existingMessages.forEach(msg => msg.remove());

    // 直接初始化产品网格
    window.initializeProductGrid(imageFolderPath)
        .then(result => {
            console.log(`产品网格初始化完成: 总共 ${result.total} 个产品`);
            if (result.added > 0) {
                console.log(`添加了 ${result.added} 个额外栏位`);
            }
        })
        .catch(error => {
            console.error('产品网格初始化失败:', error);
        });
};

// 初始化新产品的交互功能
window.initializeNewProducts = function() {
    console.log('开始初始化新产品的交互功能');

    // 为新产品添加点击事件（产品图片点击打开模态框）
    document.querySelectorAll('.product-image:not([data-initialized])').forEach(img => {
        img.addEventListener('click', async function() {
            const card = this.closest('.product-card');
            const id = card.dataset.id;
            const name = card.dataset.name;
            const price = card.dataset.price;
            const image = card.dataset.image;
            const desc = card.querySelector('p');
            const tags = card.querySelectorAll('.tag');

            // 获取图片的基础路径（去掉扩展名）
            const basePath = image.replace(/\.[^/.]+$/, "");

            // 使用动态图片检测功能
            let detectedImageUrl = image; // 默认使用原始路径
            if (typeof window.detectModalImage === 'function') {
                try {
                    detectedImageUrl = await window.detectModalImage(basePath);
                    console.log('新产品模态框检测到的图片路径:', detectedImageUrl);
                } catch (error) {
                    console.error('新产品模态框图片检测出错:', error);
                }
            }

            // 检测子图片
            let subImages = [];
            if (typeof window.detectSubImages === 'function') {
                try {
                    subImages = await window.detectSubImages(basePath);
                    console.log(`新产品检测到 ${subImages.length} 个子图片`);
                } catch (error) {
                    console.error('新产品子图片检测出错:', error);
                }
            }

            // 触发模态框显示事件
            const modalEvent = new CustomEvent('showProductModal', {
                detail: {
                    id, name, price,
                    image: detectedImageUrl,
                    description: desc ? desc.textContent : '',
                    tags: Array.from(tags).map(tag => tag.textContent),
                    basePath: basePath,
                    subImages: subImages
                }
            });

            document.dispatchEvent(modalEvent);
        });

        // 标记为已初始化
        img.setAttribute('data-initialized', 'true');
    });

    // 为新产品添加购物车按钮事件
    document.querySelectorAll('.add-to-cart:not([data-initialized])').forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.product-card');
            const id = card.dataset.id;
            const name = card.dataset.name;
            const price = parseFloat(card.dataset.price);
            const image = card.dataset.image;

            // 触发添加到购物车事件
            const cartEvent = new CustomEvent('addToCart', {
                detail: { id, name, price, image, quantity: 1 }
            });

            document.dispatchEvent(cartEvent);

            // 显示添加成功提示
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check mr-2"></i> 已添加!';
            this.style.backgroundColor = '#28a745';

            setTimeout(() => {
                this.innerHTML = originalText;
                this.style.backgroundColor = '';
            }, 1500);
        });

        // 标记为已初始化
        btn.setAttribute('data-initialized', 'true');
    });

    console.log('新产品交互功能初始化完成');
};

(function() {
  "use strict";

  /**
   * Apply .scrolled class to the body as the page is scrolled down
   */
  function toggleScrolled() {
    const selectBody = document.querySelector('body');
    const selectHeader = document.querySelector('#header');
    if (!selectHeader.classList.contains('scroll-up-sticky') && !selectHeader.classList.contains('sticky-top') && !selectHeader.classList.contains('fixed-top')) return;
    window.scrollY > 100 ? selectBody.classList.add('scrolled') : selectBody.classList.remove('scrolled');
  }

  document.addEventListener('scroll', toggleScrolled);
  window.addEventListener('load', toggleScrolled);

  /**
   * Mobile nav toggle
   */
  const mobileNavToggleBtn = document.querySelector('.mobile-nav-toggle');

  function mobileNavToogle() {
    document.querySelector('body').classList.toggle('mobile-nav-active');
    mobileNavToggleBtn.classList.toggle('bi-list');
    mobileNavToggleBtn.classList.toggle('bi-x');
  }
  mobileNavToggleBtn.addEventListener('click', mobileNavToogle);

  /**
   * Hide mobile nav on same-page/hash links
   */
  document.querySelectorAll('#navmenu a').forEach(navmenu => {
    navmenu.addEventListener('click', () => {
      if (document.querySelector('.mobile-nav-active')) {
        mobileNavToogle();
      }
    });

  });

  /**
   * Toggle mobile nav dropdowns
   */
  document.querySelectorAll('.navmenu .toggle-dropdown').forEach(navmenu => {
    navmenu.addEventListener('click', function(e) {
      e.preventDefault();
      this.parentNode.classList.toggle('active');
      this.parentNode.nextElementSibling.classList.toggle('dropdown-active');
      e.stopImmediatePropagation();
    });
  });

  /**
   * Preloader
   */
  const preloader = document.querySelector('#preloader');
  if (preloader) {
    window.addEventListener('load', () => {
      preloader.remove();
    });
  }

  /**
   * Scroll top button
   */
  let scrollTop = document.querySelector('.scroll-top');

  function toggleScrollTop() {
    if (scrollTop) {
      window.scrollY > 100 ? scrollTop.classList.add('active') : scrollTop.classList.remove('active');
    }
  }
  scrollTop.addEventListener('click', (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  window.addEventListener('load', toggleScrollTop);
  document.addEventListener('scroll', toggleScrollTop);

  /**
   * Animation on scroll function and init
   */
  function aosInit() {
    AOS.init({
      duration: 600,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    });
  }
  window.addEventListener('load', aosInit);

  /**
   * Initiate glightbox
   */
  const glightbox = GLightbox({
    selector: '.glightbox'
  });

  /**
   * Initiate Pure Counter
   */
  new PureCounter();

  /**
   * Init swiper sliders
   */
  function initSwiper() {
    document.querySelectorAll(".init-swiper").forEach(function(swiperElement) {
      let config = JSON.parse(
        swiperElement.querySelector(".swiper-config").innerHTML.trim()
      );

      if (swiperElement.classList.contains("swiper-tab")) {
        initSwiperWithCustomPagination(swiperElement, config);
      } else {
        new Swiper(swiperElement, config);
      }
    });
  }

  window.addEventListener("load", initSwiper);

  /**
   * Init isotope layout and filters
   */
  document.querySelectorAll('.isotope-layout').forEach(function(isotopeItem) {
    let layout = isotopeItem.getAttribute('data-layout') ?? 'masonry';
    let filter = isotopeItem.getAttribute('data-default-filter') ?? '*';
    let sort = isotopeItem.getAttribute('data-sort') ?? 'original-order';

    let initIsotope;
    imagesLoaded(isotopeItem.querySelector('.isotope-container'), function() {
      initIsotope = new Isotope(isotopeItem.querySelector('.isotope-container'), {
        itemSelector: '.isotope-item',
        layoutMode: layout,
        filter: filter,
        sortBy: sort
      });
    });

    isotopeItem.querySelectorAll('.isotope-filters li').forEach(function(filters) {
      filters.addEventListener('click', function() {
        isotopeItem.querySelector('.isotope-filters .filter-active').classList.remove('filter-active');
        this.classList.add('filter-active');
        initIsotope.arrange({
          filter: this.getAttribute('data-filter')
        });
        if (typeof aosInit === 'function') {
          aosInit();
        }
      }, false);
    });

  });

  /**
   * Correct scrolling position upon page load for URLs containing hash links.
   */
  window.addEventListener('load', function(e) {
    if (window.location.hash) {
      if (document.querySelector(window.location.hash)) {
        setTimeout(() => {
          let section = document.querySelector(window.location.hash);
          let scrollMarginTop = getComputedStyle(section).scrollMarginTop;
          window.scrollTo({
            top: section.offsetTop - parseInt(scrollMarginTop),
            behavior: 'smooth'
          });
        }, 100);
      }
    }
  });

  /**
   * Navmenu Scrollspy
   */
  let navmenulinks = document.querySelectorAll('.navmenu a');

  function navmenuScrollspy() {
    navmenulinks.forEach(navmenulink => {
      if (!navmenulink.hash) return;
      let section = document.querySelector(navmenulink.hash);
      if (!section) return;
      let position = window.scrollY + 200;
      if (position >= section.offsetTop && position <= (section.offsetTop + section.offsetHeight)) {
        document.querySelectorAll('.navmenu a.active').forEach(link => link.classList.remove('active'));
        navmenulink.classList.add('active');
      } else {
        navmenulink.classList.remove('active');
      }
    })
  }
  window.addEventListener('load', navmenuScrollspy);
  document.addEventListener('scroll', navmenuScrollspy);

})();