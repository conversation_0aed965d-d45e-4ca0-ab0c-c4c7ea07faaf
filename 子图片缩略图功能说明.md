# 子图片缩略图功能说明

## 🎯 功能概述

实现了产品图片的子图片缩略图功能：当点击产品图片放大时，如果存在同名的子图片（如13-1.jpg、13-2.jpg），会在放大图下方显示为小的缩略图，点击缩略图可以与主图互换。

## 📁 文件命名规则

### 主图片
- 格式：`数字.扩展名`
- 示例：`13.jpg`、`24.png`、`15.jpeg`

### 子图片
- 格式：`数字-序号.扩展名`
- 示例：`13-1.jpg`、`13-2.jpg`、`24-1.png`、`24-2.png`

### 支持的图片格式
- jpg
- jpeg
- png
- webp
- avif

## 🔧 技术实现

### 1. 子图片检测函数
```javascript
// 检测产品的子图片（如13-1.jpg, 13-2.jpg等）
window.detectSubImages = function(basePath) {
    const extensions = ['jpg', 'jpeg', 'png', 'webp', 'avif'];
    
    async function findSubImages() {
        const subImages = [];
        
        // 检测从-1到-10的子图片
        for (let i = 1; i <= 10; i++) {
            for (const ext of extensions) {
                const subImageUrl = `${basePath}-${i}.${ext}`;
                const exists = await testImage(subImageUrl);
                if (exists) {
                    console.log(`找到子图片: ${subImageUrl}`);
                    subImages.push({
                        url: subImageUrl,
                        index: i,
                        extension: ext
                    });
                    break; // 找到一个格式就跳出内层循环
                }
            }
        }
        
        console.log(`总共找到 ${subImages.length} 个子图片`);
        return subImages;
    }
    
    return findSubImages();
};
```

### 2. 缩略图创建函数
```javascript
// 创建和管理子图片缩略图的函数
window.createSubImageThumbnails = function(subImages, mainImageElement, modalContainer) {
    // 移除现有的缩略图容器
    const existingThumbnails = modalContainer.querySelector('.sub-images-container');
    if (existingThumbnails) {
        existingThumbnails.remove();
    }
    
    if (subImages.length === 0) {
        return; // 没有子图片，不创建缩略图
    }
    
    // 创建缩略图容器和缩略图...
};
```

### 3. 模态框集成
- **自动检测**: 在模态框打开时自动检测子图片
- **动态创建**: 如果找到子图片，动态创建缩略图容器
- **智能布局**: 缩略图显示在主图片下方，居中排列

## 🎨 界面设计

### 缩略图样式
- **尺寸**: 60x60像素
- **布局**: 水平排列，居中对齐
- **间距**: 8px间隔
- **边框**: 2px透明边框，悬浮时变为蓝色
- **效果**: 悬浮时放大1.1倍，透明度变为1

### 交互效果
- **悬浮**: 缩略图放大并高亮边框
- **点击**: 淡出淡入效果，主图与缩略图互换
- **动画**: 0.3秒过渡动画

## 📄 页面支持

### 已更新的页面
1. **BaseballCap-list1.html** ✅
   - 使用`detectAndPlayModalMedia`函数
   - 自动支持子图片功能

2. **BaseballCap-list2.html** ✅
   - 添加了子图片检测和缩略图创建

3. **BaseballCap-list3.html** ✅
   - 使用`detectAndPlayModalMedia`函数
   - 自动支持子图片功能

4. **PanamaCap-list1.html** ✅
   - 使用`detectAndPlayModalMedia`函数
   - 自动支持子图片功能

5. **PanamaCap-list2.html** ✅
   - 添加了子图片检测和缩略图创建

6. **PanamaCap-list3.html** ✅
   - 添加了子图片检测和缩略图创建

### 动态加载产品
- **新产品**: 通过`initializeNewProducts`函数自动支持
- **事件传递**: 子图片信息通过`showProductModal`事件传递

## 🧪 测试用例

### 已创建的测试文件
在`assets/img/cap/panama/list1/`文件夹中创建了以下测试子图片：

1. **产品13的子图片**:
   - `13-1.jpg` (复制自1.jpg)
   - `13-2.jpg` (复制自2.jpg)
   - `13-3.jpg` (复制自3.jpg)

2. **产品24的子图片**:
   - `24-1.jpg` (复制自4.jpg)
   - `24-2.jpg` (复制自5.jpg)

3. **产品15的子图片**:
   - `15-1.jpg` (复制自6.jpg)
   - `15-2.jpg` (复制自7.jpg)

### 测试步骤
1. 打开PanamaCap-list1.html页面
2. 点击产品13、15或24的图片
3. 查看模态框下方是否显示缩略图
4. 点击缩略图测试图片互换功能

## 🎯 功能特点

### ✅ 优点
- **自动检测**: 无需手动配置，自动检测子图片
- **智能适配**: 支持多种图片格式
- **流畅交互**: 平滑的淡入淡出效果
- **响应式**: 缩略图自动适应容器宽度
- **兼容性**: 与现有功能完全兼容

### 🔄 工作流程
1. 用户点击产品图片
2. 系统检测主图片路径
3. 自动搜索同名子图片（-1到-10）
4. 如果找到子图片，创建缩略图容器
5. 显示缩略图，支持点击互换

### 📊 性能优化
- **异步检测**: 子图片检测不阻塞主图片显示
- **智能缓存**: 图片检测结果自动缓存
- **按需加载**: 只有存在子图片时才创建缩略图

## 🚀 使用效果

现在当用户点击产品图片时：
- 如果只有主图片：正常显示放大图
- 如果有子图片：主图片下方显示缩略图，可以点击切换查看不同角度的产品图片

这大大提升了用户体验，让用户可以查看产品的多个角度和细节！
